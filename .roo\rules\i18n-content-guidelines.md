# i18n Content Guidelines für Astro + GitLab Pages

## Übersicht

Diese Regeln gelten für alle neuen Inhalte im LLM Browser Projekt, das Astro Framework mit i18n-Unterstützung und GitLab Pages CI/CD-Pipeline verwendet.

## 🌍 Unterstützte Sprachen

- **Deutsch (de)**: Standard-Sprache
- **Englisch (en)**: Vollständige Übersetzungsunterstützung  
- **Polnisch (pl)**: Vollständige Übersetzungsunterstützung

## 📁 Dateistruktur für Übersetzungen

### Translation Files
```

static/locales/           # Backup-Verzeichnis
├── de/i18n.json
├── en/i18n.json
└── pl/i18n.json
```

## 🔧 Technische Implementierung

### Dual i18n System
Das Projekt verwendet zwei komplementäre i18n-Mechanismen:

**Client-side i18n**: Für React-Komponenten mit TranslationContext

**Server-side i18n**: NICHT zu verwenden insb. für Astro-Seiten (`.astro` Dateien)


## 📝 Regeln für neue Inhalte

### 1. Translation Keys

#### ✅ DO: Konsistente Key-Struktur
```json
{
  "section": {
    "subsection": {
      "key": "Übersetzter Text"
    }
  }
}
```

#### ✅ DO: Aussagekräftige Key-Namen
```json
{
  "models": {
    "filters": {
      "search_placeholder": "Suche nach Name, Anbieter oder Modellgruppe..."
    }
  }
}
```

#### ❌ DON'T: Flache oder unklare Keys
```json
{
  "text1": "Irgendein Text",
  "btn": "Klick mich"
}
```

### 2. Astro-Seiten (.astro Dateien)

#### ✅ DO: Server-side Translation Loading
```astro
---
import fs from "fs";
import path from "path";

// Sprache aus Cookie/Browser erkennen
const cookieLang = Astro.cookies?.get("preferredLanguage")?.value;
const browserLang = Astro.request.headers.get("accept-language")?.split(",")[0]?.split("-")[0];
let currentLang = cookieLang || (["de", "en", "pl"].includes(browserLang) ? browserLang : "de");

// Übersetzungen laden
const translationsPath = path.join(process.cwd(), "public", "locales", currentLang, "i18n.json");
const fallbackPath = path.join(process.cwd(), "public", "locales", "de", "i18n.json");

let translations = {};
try {
  if (fs.existsSync(translationsPath)) {
    translations = JSON.parse(fs.readFileSync(translationsPath, "utf-8"));
  } else {
    translations = JSON.parse(fs.readFileSync(fallbackPath, "utf-8"));
  }
} catch (error) {
  console.error("Error loading translations:", error);
  translations = JSON.parse(fs.readFileSync(fallbackPath, "utf-8"));
}
---

<h1>{translations.page?.title || "Fallback Title"}</h1>
```

#### ✅ DO: Fallback-Werte verwenden
```astro
<h1>{translations.models?.header || "Models"}</h1>
<p>{translations.models?.description || "Browse and compare models"}</p>
```

#### ❌ DON'T: Hardcodierte Texte
```astro
<h1>Models</h1>  <!-- Schlecht: Nicht übersetzt -->
```

### 3. React-Komponenten

#### ✅ DO: TranslationContext verwenden
```tsx
import { useTranslation } from '../contexts/TranslationContext';

function MyComponent() {
  const { getTranslation } = useTranslation();
  
  return (
    <div>
      <h1>{getTranslation('models.header')}</h1>
      <p>{getTranslation('models.description')}</p>
    </div>
  );
}
```

#### ✅ DO: Parameter-Ersetzung
```tsx
// Translation file
{
  "models": {
    "count": "Zeige {{count}} von {{total}} Modellen"
  }
}

// Component
const text = getTranslation('models.count', { count: 10, total: 50 });
```

#### ✅ DO: TranslationProvider wrappen
```astro
<TranslationProvider
  initialTranslations={translations}
  initialLang={currentLang}
  client:only="react"
>
  <MyReactComponent />
</TranslationProvider>
```

### 4. Neue Translation Keys hinzufügen

#### ✅ DO: Alle Sprachen synchron halten
1. Key in `static/locales/de/i18n.json` hinzufügen
2. Key in `static/locales/en/i18n.json` hinzufügen  
3. Key in `static/locales/pl/i18n.json` hinzufügen

#### ✅ DO: Konsistente Struktur
```json
// Alle drei Dateien sollten dieselbe Struktur haben
{
  "new_section": {
    "title": "...",
    "description": "...",
    "actions": {
      "save": "...",
      "cancel": "..."
    }
  }
}
```

### 5. GitLab CI/CD Kompatibilität

#### ✅ DO: Build-Zeit Überlegungen
- Übersetzungen werden zur Build-Zeit geladen
- Keine dynamischen Translation-Requests zur Laufzeit
- Statische Generierung für alle Sprachen

#### ✅ DO: Asset-Pfade beachten
```javascript
// astro.config.mjs
export default defineConfig({
  site: 'https://iteratec-llm-browser-b6a964.pages.iteratec.de',
  base: '/',
  output: 'static',
  outDir: 'public',        // GitLab Pages erwartet 'public/'
  publicDir: 'static',     // Astro Assets aus 'static/'
});
```

#### ✅ DO: Translation Files in beiden Verzeichnissen
- `static/locales/` für Astro Assets

## 🚀 CI/CD Pipeline Überlegungen

### Build-Prozess
```yaml
# .gitlab-ci.yml
build:
  stage: build
  script:
    - npm ci
    - npm run generate:data    # Statische Daten generieren
    - npm run build           # Astro Build mit i18n
  artifacts:
    paths:
      - public/               # Enthält gebaute Site + Übersetzungen
```

### ✅ DO: Pre-Build Validierung
```bash
# Vor dem Build prüfen
npm run lint:check           # ESLint für Code-Qualität
npm run test:run            # Tests ausführen
```

## 🔍 Testing & Validierung

### ✅ DO: Übersetzungen testen
1. Alle drei Sprachen im Browser testen
2. Fallback-Verhalten prüfen
3. Parameter-Ersetzung validieren
4. Build-Prozess lokal testen

### ✅ DO: Lokale Entwicklung
```bash
npm run dev                 # Entwicklungsserver
# Sprache über Browser-DevTools oder localStorage ändern
localStorage.setItem('preferredLanguage', 'en');
```

## ⚠️ Häufige Fehler vermeiden

### ❌ DON'T: Inkonsistente Translation Files
- Fehlende Keys in einer Sprache
- Unterschiedliche Struktur zwischen Sprachen
- Vergessene Backup-Files in `static/locales/`

### ❌ DON'T: Runtime Translation Loading
- Keine fetch() Requests für Übersetzungen
- Keine dynamischen Translation-Imports zur Laufzeit

### ❌ DON'T: Hardcodierte Sprach-Logik
```astro
---
const currentLang = "de";  // Schlecht: Hardcodiert
---
```

## 📋 Checkliste für neue Inhalte

- [ ] Translation Keys in allen drei Sprachen hinzugefügt
- [ ] Fallback-Werte in Astro-Komponenten definiert
- [ ] React-Komponenten verwenden TranslationContext
- [ ] Lokale Tests in allen Sprachen durchgeführt
- [ ] Build-Prozess lokal getestet
- [ ] CI/CD Pipeline erfolgreich durchlaufen
- [ ] Backup-Files in `static/locales/` aktualisiert

## 🔗 Referenzen

- **i18n Implementation Guide**: `memory-bank/i18n.md`
- **Feature Documentation**: `docs/feature-i18n-recommendations/`
- **GitLab CI/CD**: `.gitlab-ci.yml`
- **Astro Config**: `astro.config.mjs`

# i18n Implementation Guide - LLM Browser Project

## Overview

This project uses a **dual i18n mechanism** optimized for Astro's hybrid architecture:

1. **Server-side i18n**: For Astro pages (`.astro` files) using direct translation object access
2. **Client-side i18n**: For React components using TranslationContext with `t()` function

Both mechanisms share the same translation files and work together seamlessly.

## Supported Languages

- **German (de)**: Default language
- **English (en)**: Full translation support
- **Polish (pl)**: Full translation support

## File Structure

```
public/locales/
├── de/i18n.json          # German translations (default)
├── en/i18n.json          # English translations
└── pl/i18n.json          # Polish translations

src/contexts/
└── TranslationContext.tsx # React translation context

src/components/
├── LanguageSelectorIsland.tsx    # Language selector wrapper
└── ui/language-selector.tsx      # Language selector component

src/layouts/
└── Layout.astro          # Main layout with server-side i18n setup
```

## Translation File Structure

Each `i18n.json` file follows this structure:

```json
{
  "site": {
    "title": "LLM Browser",
    "description": "Browse and compare LLM models"
  },
  "nav": {
    "models": "Models",
    "blog": "Blog",
    "recommendations": "Recommendations",
    "benchmarks": "All Benchmarks",
    "api_usage": "API Usage@iteratec"
  },
  "footer": {
    "copyright": "© 2025 LLM Browser",
    "built_with": "Built with Astro, React, TypeScript and Tailwind CSS"
  },
  "language_selector": {
    "label": "Language",
    "de": "Deutsch",
    "en": "English", 
    "pl": "Polski"
  },
  "models": {
    "header": "LLM Models",
    "description": "Browse and compare available models",
    "filters": { /* filter options */ },
    "stats": { /* statistics labels */ },
    "comparison": { /* comparison table labels */ },
    "capabilities": { /* capability labels */ }
  },
  "blog": { /* blog-specific translations */ },
  "recommendations": { /* recommendations page translations */ },
  "benchmark": { /* benchmark page translations */ },
  "qc": { /* quality control labels */ },
  "components": {
    "collapsible_header": {
      "show_info": "Show Info",
      "hide_info": "Hide Info"
    }
  }
}
```

## Server-side i18n (Astro Pages)

### Setup in Layout.astro

The main layout handles:
- Language detection from cookies/browser
- Translation file loading
- Global variable setup for client-side

### Usage in .astro Files

```astro
---
// Import required modules for translation loading
import fs from "fs";
import path from "path";

// Language detection (if not in Layout.astro)
const cookieLang = Astro.cookies?.get("preferredLanguage")?.value;
const browserLang = Astro.request.headers.get("accept-language")?.split(",")[0]?.split("-")[0];
let currentLang = cookieLang || (["de", "en", "pl"].includes(browserLang) ? browserLang : "de");

// Load translations
let translations = {};
try {
  const translationsPath = path.join(process.cwd(), "public", "locales", currentLang, "i18n.json");
  const fileContent = fs.readFileSync(translationsPath, "utf-8").trim();
  translations = JSON.parse(fileContent);
} catch (error) {
  console.error("Error loading translations:", error);
}
---

<h1>{translations.models?.header || "Models"}</h1>
<p>{translations.models?.description || "Browse models"}</p>

<!-- Dynamic content with placeholders -->
<p>{translations.recommendations?.model_count?.replace("{count}", modelCount) || `${modelCount} models available`}</p>
```

### Required Imports for Astro Pages

```astro
---
import fs from "fs";
import path from "path";
// Add fsSync import if using synchronous operations
import * as fsSync from 'node:fs';
---
```

## Client-side i18n (React Components)

### Setup

React components use the TranslationContext provided by Layout.astro:

```tsx
import { useTranslation } from "@/contexts/TranslationContext";

export function MyComponent() {
  const { t, currentLang, changeLanguage } = useTranslation();
  
  return (
    <div>
      <h1>{t('models.header')}</h1>
      <p>{t('models.description')}</p>
      <button onClick={() => changeLanguage('en')}>
        Switch to English
      </button>
    </div>
  );
}
```

### Island Wrapper Pattern

For React components used in Astro pages:

```tsx
// MyComponentIsland.tsx
import { TranslationProvider } from "@/contexts/TranslationContext";
import { MyComponent } from "./MyComponent";

export function MyComponentIsland() {
  return (
    <TranslationProvider>
      <MyComponent />
    </TranslationProvider>
  );
}
```

Usage in Astro:
```astro
<MyComponentIsland client:load />
```

## Language Detection Priority

1. **Cookie**: `preferredLanguage` cookie value
2. **Browser Language**: `Accept-Language` header (first language)
3. **Default**: German (`de`)

## Language Switching Mechanism

- Language changes trigger a **page reload** to ensure both server and client translations update
- New language is stored in `preferredLanguage` cookie
- Server-side re-renders with new language on reload

## Translation Key Patterns

### Nested Keys
```json
{
  "models": {
    "comparison": {
      "title": "Model Comparison"
    }
  }
}
```

Access: `t('models.comparison.title')` or `translations.models?.comparison?.title`

### Dynamic Content
```json
{
  "recommendations": {
    "model_count": "We have {count} models available"
  }
}
```

Usage: `t('recommendations.model_count', { count: 42 })` or `translations.recommendations?.model_count?.replace("{count}", "42")`

## Best Practices

### For Astro Pages
1. Always add required imports (`fs`, `path`)
2. Use optional chaining: `translations.section?.key`
3. Provide fallback values: `translations.nav?.models || "Models"`
4. Handle translation loading errors with try-catch

### For React Components
1. Use `useTranslation()` hook
2. Wrap island components with TranslationProvider
3. Use `t()` function for all text content
4. Test translation key existence before using

### Translation Files
1. Keep all three language files in sync
2. Use consistent key naming (snake_case)
3. Group related translations in sections
4. Provide meaningful fallback text

## Common Patterns

### Page Headers
```astro
<h1>{translations.models?.header || "Models"}</h1>
<p class="text-muted-foreground">
  {translations.models?.description || "Browse and compare models"}
</p>
```

### Navigation Links
```astro
<a href="/models/">
  {translations.nav?.models || "Models"}
</a>
```

### Dynamic Lists
```astro
{items.map(item => (
  <div>
    <h3>{item.name}</h3>
    <p>{translations.models?.stats?.total || "Total"}: {item.count}</p>
  </div>
))}
```

### React Component Translation
```tsx
function ModelCard({ model }) {
  const { t } = useTranslation();
  
  return (
    <div>
      <h3>{model.name}</h3>
      <p>{t('models.stats.context_window')}: {model.contextWindow}</p>
      <button>{t('models.comparison.reset_selection')}</button>
    </div>
  );
}
```
### Critical Fallback Pattern for React Components

**⚠️ IMPORTANT**: React components must use a fallback pattern to handle translation loading race conditions.

#### The Problem
React components using direct `t(translations, 'key')` calls may show translation keys instead of values because:
- TranslationContext might be empty during initial render
- Global window translations are always available as fallback

#### The Solution
Always implement this fallback pattern in React components:

```tsx
// Add global window interface declarations
declare global {
  interface Window {
    translations: any;
    currentLang: string;
  }
}

function MyComponent() {
  const { translations } = useTranslation();
  
  // Helper function with fallback logic
  const getTranslation = (key: string, params?: Record<string, any>) => {
    // Try context translations first
    let result = t(translations, key);
    
    // Fallback to global window translations
    if (result === key && typeof window !== 'undefined' && window.translations) {
      result = t(window.translations, key);
    }
    
    // Handle parameter replacement
    if (params && result !== key) {
      Object.entries(params).forEach(([param, value]) => {
        result = result.replace(`{${param}}`, String(value));
      });
    }
    
    return result;
  };
  
  return (
    <div>
      <h1>{getTranslation('models.header')}</h1>
      <p>{getTranslation('models.filtered_count', { count: 42 })}</p>
    </div>
  );
}
```

### Translation Keys Showing Instead of Values

**Symptom**: React components display translation keys (e.g., "models.header") instead of translated text.

**Root Cause**: TranslationContext is empty during initial render due to translation loading race condition.

**Solution**: Implement the Critical Fallback Pattern (see above section).

**Quick Fix**:
1. Add global window interface declarations
2. Create `getTranslation` helper function with fallback logic
3. Replace all `t(translations, 'key')` calls with `getTranslation('key')`

**Example Fix**:
```tsx
// Before (shows translation keys)
const text = t(translations, 'models.header');

// After (shows translated text)
const getTranslation = (key: string) => {
  let result = t(translations, key);
  if (result === key && typeof window !== 'undefined' && window.translations) {
    result = t(window.translations, key);
  }
  return result;
};
const text = getTranslation('models.header');
```
#### Why This Works
1. **Context First**: Tries TranslationContext translations (preferred)
2. **Global Fallback**: Falls back to window.translations if context is empty
3. **Key Fallback**: Returns the key itself if no translation found
4. **Parameter Support**: Handles dynamic content replacement

#### When to Use
- **Always** in React components that display translated text
- **Especially** in island components that hydrate on the client
- **Required** for components showing translation keys instead of values

## Troubleshooting

### Common Issues

1. **Missing translations**: Check console for "Translation key not found" warnings
2. **404 errors**: Ensure translation files exist in `public/locales/{lang}/i18n.json`
3. **React hydration errors**: Wrap island components with TranslationProvider
4. **Language not switching**: Check cookie storage and page reload mechanism
5. **Language switching only works on some pages**: Check prerendering configuration (see Critical Issue below)

### Critical Issue: Prerendering vs Language Switching

**⚠️ CRITICAL**: Language switching will NOT work on prerendered pages.

#### The Problem
- Astro's `prerender: true` (default) generates static HTML at build time
- Static pages cannot access runtime cookies containing language preferences
- Result: Pages always show default language (German) regardless of user selection

#### The Solution
**ALWAYS** add `export const prerender = false;` to pages that need language switching:

```astro
---
export const prerender = false; // REQUIRED for language switching

// Rest of your page logic...
const cookieLang = Astro.cookies?.get("preferredLanguage")?.value;
// ...
---
```

#### Affected Page Types
- **Blog pages**: `src/pages/blog/index.astro`, `src/pages/blog/[slug].astro`
- **Recommendations**: `src/pages/recommendations/index.astro`
- **Benchmarks**: `src/pages/benchmarks/index.astro`, `src/pages/benchmarks/*/index.astro`
- **Any page using language switching**

#### Pages Already Configured Correctly
- `src/pages/index.astro` ✅
- `src/pages/models/index.astro` ✅

#### Dynamic Routes Special Handling
For dynamic routes like `[slug].astro`, when disabling prerendering:

1. Remove `getStaticPaths()` function
2. Add dynamic entry lookup:
```astro
---
export const prerender = false;

const { slug } = Astro.params;
const entries = await getCollection('blog');
const entry = entries.find(e => e.slug === slug);

if (!entry) {
  return Astro.redirect('/404');
}
---
```

#### Cookie Name Consistency
Ensure all pages use the same cookie name: `"preferredLanguage"` (not `"language"`)

```astro
// Correct
const cookieLang = Astro.cookies?.get("preferredLanguage")?.value;

// Incorrect
const cookieLang = Astro.cookies?.get("language")?.value;
```

### Debug Information

The system logs extensive debug information:
- Server-side language detection
- Translation file loading
- Client-side translation initialization
- Translation key access attempts

### File Import Errors

If you see "Cannot resolve module" errors in Astro pages:
```astro
---
// Add these imports
import fs from "fs";
import path from "path";
import * as fsSync from 'node:fs'; // If using sync operations
---
```

## Adding New Languages

1. Create new translation file: `public/locales/{lang}/i18n.json`
2. Copy structure from existing file (e.g., `en/i18n.json`)
3. Translate all values
4. Add language to supported list in Layout.astro language detection
5. Add language option to LanguageSelector component

## Adding New Translation Keys

1. Add key to all three language files (`de`, `en`, `pl`)
2. Use consistent key naming and nesting
3. Test in both Astro pages and React components
4. Provide meaningful fallback values
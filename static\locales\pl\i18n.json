{"site": {"title": "<PERSON><PERSON>", "description": "LLM Browser - Porównuj i oceniaj modele językowe"}, "nav": {"models": "Models", "blog": "Blog", "recommendations": "Rekomendacje", "benchmarks": "Ben<PERSON>mark<PERSON>", "api_usage": "Użycie API@iteratec"}, "footer": {"copyright": "© 2025 LLM Browser - Vise-Coding", "built_with": "Zbudowane z użyciem Astro, React, TypeScript i Tailwind CSS"}, "language_selector": {"label": "<PERSON><PERSON>bierz język", "de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "pl": "<PERSON><PERSON>"}, "language": "Język", "redirect": "Przekierowanie do przeglądu modeli...", "meta_description": "LLM Browser - Porównuj i oceniaj modele językowe", "vise_coding": "Vise-Coding", "loading": "Ładowanie...", "models": {"header": "Przeglądarka modeli LLM", "description": "Od<PERSON><PERSON><PERSON> {{count}} modeli AI z szczegółowymi informacjami o cenach, możliwościach i wynikach benchmarków. Celem jest uczynienie wyboru odpowiednich modeli do codziennej pracy bardziej interaktywnym, łatwiejszym i bardziej przejrzystym. W przeciwnym razie informacje trzeba zbierać z wielu źródeł. Na podstawie danych na żywo z iteraGPT (LiteLLM) i kart modeli generowanych przez AI z różnych źródeł.", "filters": {"all": "Wszystkie", "security": "Bezpieczeństwo", "mode": "<PERSON><PERSON>", "chat": "Cha<PERSON>", "completion": "Completion", "embedding": "Embedding", "image_generation": "Generowanie obrazów", "search_placeholder": "<PERSON><PERSON><PERSON> nazwy, dostawcy lub grupy modeli...", "of": "z", "models": "modeli"}, "stats": {"total_models": "Łączna liczba modeli", "benchmarks": "Ben<PERSON>mark<PERSON>", "average_score": "Średni wynik", "top_performer": "Najlepszy wykonawca", "filtered_count": "{{filtered}} z {{total}} modeli"}, "comparison": {"title": "Porównanie modeli ({{count}} modeli)", "reset_selection": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>ó<PERSON>", "property": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "provider": "Dostawca", "litellm_availability": "Dostępność LiteLLM", "available": "Dostępny", "model_card_only": "Tylko karta modelu", "context_window": "Okno kontekstowe", "max_output_tokens": "Maks. <PERSON> wyjściowych", "input_cost": "Koszt wejścia (za 1M tokenów)", "output_cost": "Koszt wyjścia (za 1M tokenów)", "yes": "Tak", "no": "<PERSON><PERSON>", "capabilities": "<PERSON><PERSON><PERSON><PERSON>ści", "supported_platforms": "Wspierane platformy", "metric": "Metryka", "range": "<PERSON><PERSON><PERSON>", "no_details_available": "Brak szczegółowych informacji.", "other_benchmarks": "Inne benchmarki", "at": "przy", "and": "i", "well_formed_code": "poprawnie sformatowanego kodu", "category": "Kategoria", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variants": "Warianty", "not_available": "N/D", "aider_polyglot_short": "Aider-Polyglot (o)", "website": "Strona internetowa", "paper": "Publikacja", "aider_benchmark": {"title": "Benchmark polyglot Aidera", "description": "<PERSON><PERSON><PERSON> zdolność modeli do edycji i ulepszania kodu w różnych językach programowania.", "metric": "Wskaźnik powodzenia (2. pr<PERSON>ba)", "range": "0-100%", "fallback_description": "Benchmark kodowania Aidera - mierzy zdolność modeli do edycji i ulepszania kodu."}}, "capabilities": {"vision": "<PERSON><PERSON><PERSON><PERSON>", "pdf_input": "Wejście PDF", "audio_input": "Wejście audio", "audio_output": "Wyjście audio", "embedding_image": "Osadzanie obrazów", "function_calling": "Wywoływanie funkcji", "prompt_caching": "Buforowanie promptów", "reasoning": "Rozumowanie", "system_messages": "Komunikaty systemowe"}, "table": {"pagination": {"showing": "Wyświ<PERSON>lanie {{start}} do {{end}} z {{total}} wpisów", "previous": "Poprzednia", "next": "Następna"}, "headers": {"select": "<PERSON><PERSON><PERSON><PERSON>", "security": "Bezpieczeństwo", "model_card": "Karta modelu", "litellm_status": "Status LiteLLM", "name": "Nazwa", "provider": "Dostawca", "mode": "<PERSON><PERSON>", "context": "Kontekst", "max_output": "Maks. wyjście", "input_cost_per_million": "Koszt wejścia/1M", "output_cost_per_million": "Koszt wyjścia/1M", "polyglot_score": "Wynik Polyglot", "support": "<PERSON><PERSON><PERSON><PERSON>", "details": "Szczegóły", "fullscreen": "Pełny ekran", "show_filters": "Pokaż filtry"}, "tooltips": {"confidential": "<PERSON><PERSON><PERSON>", "internal": "Wewnętrzny", "public": "Publiczny", "model_card_available": "Karta modelu dos<PERSON>ę<PERSON>", "deprecated": "Przestarzały", "shutdown_date": "Wyłączenie: {{date}}", "litellm_available": "Dostępny przez LiteLLM", "model_card_only": "Tylko karta modelu", "chat": "Cha<PERSON>", "embedding": "Osadzanie", "image": "<PERSON><PERSON><PERSON>", "vision_processing": "Widzenie/Przetwarzanie obrazów", "pdf_input": "Wejście PDF", "audio_input": "Wejście audio", "audio_output": "Wyjście audio", "embedding_image_input": "Wejście obrazów do osadzania", "function_calling": "Wywoływanie funkcji", "prompt_caching": "Buforowanie promptów", "reasoning": "Rozumowanie", "system_messages": "Komunikaty systemowe", "capability_vision": "Widzenie/Przetwarzanie obrazów", "capability_pdf_input": "Wejście PDF", "capability_audio_input": "Wejście audio", "capability_audio_output": "Wyjście audio", "capability_embedding_image_input": "Wejście obrazów do osadzania", "capability_function_calling": "Wywoływanie funkcji", "capability_prompt_caching": "Buforowanie promptów", "capability_reasoning": "Rozumowanie", "capability_system_messages": "Komunikaty systemowe"}, "empty_state": "Nie znaleziono modeli pasujących do kryteriów wyszukiwania.", "select_model": "W<PERSON>bierz model {{name}}", "details_button": "Szczegóły"}}, "blog": {"title": "LLM Blog", "description": "Najnowsze informacje o modelach AI, notatki o wydaniach i analizy benchmarków. Bądź na bieżąco z najnowszymi wydarzeniami w świecie LLM.", "sectionModelAnalysis": "<PERSON><PERSON><PERSON> <PERSON>i", "sectionModelAnalysisDesc": "Szczegółowe recenzje najnowszych modeli AI", "sectionReleaseNotes": "Notatki o wydaniach", "sectionReleaseNotesDesc": "Najnowsze aktualizacje i zmiany w modelach", "sectionBenchmarkAnalysis": "<PERSON><PERSON><PERSON> benchmarków", "sectionBenchmarkAnalysisDesc": "Dogłębne oceny testów wydajności", "sectionIndustryNews": "Wiadomości branżowe", "sectionIndustryNewsDesc": "Ważne wydarzenia na rynku AI", "stats": {"articles": "Artykuły", "categories": "<PERSON><PERSON><PERSON>", "featured": "Polecane posty", "tags": "Tagi"}, "featured_articles": "Polecane artykuły", "loading_articles": "Ładowanie artykułów...", "articles": "<PERSON><PERSON><PERSON>ł<PERSON>", "found": "znaleziono", "clear_all_filters": "<PERSON><PERSON><PERSON><PERSON>ść wszystkie filtry", "no_articles_found": "Nie znaleziono artykułów", "try_different_search": "Spróbuj innych słów kluczowych lub filtrów.", "no_articles_available": "Brak dostępnych artykułów na blogu.", "reset_filters": "<PERSON><PERSON><PERSON><PERSON> filtry", "previous": "Poprzedni", "next": "Następny"}, "recommendations": {"title": "Rekomendacje dla firm", "description": "Inteligentny system rekomendacji dla {{totalModels}} modeli LLM od {{totalProviders}} dostawców. Na podstawie standardowych przypadków użycia w firmach rekomendowane są optymalne modele do różnych scenariuszy. Uwzględniana jest wydaj<PERSON>ść w benchmarkach, m<PERSON><PERSON><PERSON><PERSON>ś<PERSON>, koszty i inne czynniki dla świadomych decyzji.", "availableModels": "Dostępne modele", "providers": "<PERSON><PERSON><PERSON><PERSON>", "gaStatus": "Status GA", "avgInputCost": "Śr. k<PERSON><PERSON><PERSON>", "avgOutputCost": "Śr. kos<PERSON>t wyj<PERSON>", "perMillion": "za 1M tokenów", "pageTitle": "Rekomendacje modeli dla firm", "pageDescription": "Inteligentne rekomendacje wyboru optymalnego modelu LLM na podstawie standardowych przypadków użycia w firmach. Uwzględnia wydajność, koszty, możliwości i inne czynniki.", "topUseCases": "Najlepsze przypadki użycia:", "benchmarksUsed": "Użyte benchmarki:", "requiredCapabilities": "Wymagane możliwości:", "category": "Kategoria:", "priority": {"high": "<PERSON><PERSON><PERSON>", "medium": "Średni", "low": "<PERSON><PERSON>", "label": "Priorytet"}, "topRecommendations": "Najlepsze rekomendacje:", "costEffectiveness": {"high": "Opłacalne", "medium": "Standardowe", "low": "<PERSON><PERSON>ie"}, "qualityCheck": "<PERSON><PERSON><PERSON><PERSON>", "stats": {"useCases": "Przypadki użycia", "models": "<PERSON>e", "recommendations": "Rekomendacje", "excellent": "Doskonałe", "avgPerUseCase": "Śr. na przypadek"}, "tabs": {"overview": "Przegląd", "topModels": "Najlepsze modele", "highPriority": "Krytyczne przypadki użycia", "allUseCases": "Wszystkie przypadki użycia"}, "sections": {"bestOverallModels": "Najlepsze modele ogólne", "bestOverallDescription": "Modele z najlepszą średnią wydajnością we wszystkich przypadkach użycia.", "criticalUseCases": "Krytyczne przypadki użycia", "criticalDescription": "Rekomendacje dla biznesowo krytycznych przypadków użycia o wysokim priorytecie.", "allUseCasesTitle": "Wszystkie przypadki użycia", "allUseCasesDescription": "Szczegółowe rekomendacje dla każdego standardowego przypadku użycia."}}, "benchmark": {"title": "Wyniki benchmarków", "description": "Szczegółowa analiza wyników benchmarku Polyglot dla {{totalBenchmarks}} testów benchmarkowych.", "testedModels": "Przetestowane modele", "averageScore": "Średni wynik", "highestScore": "Najwyższy wynik", "testCases": "Przypadki testowe", "about": "O benchmarku Polyglot:", "aboutText1": "Ten benchmark opiera się na zadaniach programistycznych Exercism i testuje zdolność modeli językowych do rozwiązywania złożonych problemów programistycznych w 6 różnych językach:", "languages": "C++, Go, Java, JavaScript, Python i Rust", "aboutText2": "Benchmark obejmuje {{hardest}} najtrudniejszych zadań z łącznie {{total}} dostępnych problemów Exercism i został zaprojektowany tak, aby był znacznie trudniejszy niż wcześniejsze benchmarki. Wyniki opierają się na liczbie pomyślnie rozwiązanych problemów i zapewniają precyzyjną ocenę możliwości edycji kodu przez nowoczesne LLM."}, "qc": {"title": "Porównanie benchmarków modeli LLM", "header": "Porównanie benchmarków", "description": "Szczegółowa analiza benchmarków dla {{modelCount}} modeli LLM. Porównaj wydajność różnych modeli w {{benchmarkCount}} różnych benchmarkach. Ten przegląd umożliwia bezpośrednie porównanie rzeczywistych wartości benchmarków z kart modeli.", "availableBenchmarks": "<PERSON><PERSON><PERSON><PERSON><PERSON> benchmarki", "avgBenchmarksPerModel": "Śr. benchmarków/model", "mostCommonBenchmark": "Na<PERSON><PERSON><PERSON><PERSON><PERSON> benchmark", "modelsWithBenchmarks": "Modele z benchmarkami", "topBenchmarks": "Top 5 benchmarków (wg dostępności)", "models": "<PERSON>e"}, "components": {"collapsible_header": {"show_info": "Pokaż informacje", "hide_info": "<PERSON><PERSON><PERSON><PERSON>"}}}